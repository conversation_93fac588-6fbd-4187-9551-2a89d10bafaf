<template>
  <div class="dify-management">
    <div class="header">
      <h2>Dify工作流管理</h2>
      <p class="description">配置和管理Dify工作流实例，实现AI功能扩展</p>
    </div>

    <el-tabs v-model="activeTab" class="dify-tabs">
      <!-- Dify实例管理 -->
      <el-tab-pane label="实例管理" name="instances">
        <div class="tab-content">
          <div class="toolbar">
            <el-button type="primary" @click="showInstanceDialog = true">
              <el-icon><Plus /></el-icon>
              添加实例
            </el-button>
            <el-button @click="refreshInstances">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <div class="filters">
              <el-switch
                v-model="showEnabledOnly"
                @change="loadInstances"
                active-text="仅显示启用"
                inactive-text="显示全部"
              />
            </div>
          </div>

          <el-table 
            :data="instances" 
            v-loading="instancesLoading"
            class="instances-table"
          >
            <el-table-column prop="name" label="实例名称" min-width="120">
              <template #default="{ row }">
                <div class="instance-name">
                  <span>{{ row.name }}</span>
                  <el-tag 
                    :type="getStatusType(row.status)" 
                    size="small"
                    class="status-tag"
                  >
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="base_url" label="API地址" min-width="200">
              <template #default="{ row }">
                <el-text class="url-text" truncated>{{ row.base_url }}</el-text>
              </template>
            </el-table-column>
            
            <el-table-column prop="description" label="描述" min-width="150">
              <template #default="{ row }">
                <el-text truncated>{{ row.description || '-' }}</el-text>
              </template>
            </el-table-column>
            
            <el-table-column prop="enabled" label="状态" width="80">
              <template #default="{ row }">
                <el-switch
                  v-model="row.enabled"
                  @change="toggleInstanceStatus(row)"
                  :disabled="instancesLoading"
                />
              </template>
            </el-table-column>
            
            <el-table-column prop="last_test_at" label="最后测试" width="120">
              <template #default="{ row }">
                <el-text size="small">
                  {{ row.last_test_at ? formatTime(row.last_test_at) : '-' }}
                </el-text>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button-group>
                  <el-button 
                    size="small" 
                    @click="testInstance(row)"
                    :loading="testingInstances.has(row.id)"
                  >
                    测试
                  </el-button>
                  <el-button 
                    size="small" 
                    @click="editInstance(row)"
                  >
                    编辑
                  </el-button>
                  <el-button 
                    size="small" 
                    type="danger" 
                    @click="deleteInstance(row)"
                  >
                    删除
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 工作流配置 -->
      <el-tab-pane label="工作流配置" name="workflows">
        <div class="tab-content">
          <div class="toolbar">
            <el-button 
              type="primary" 
              @click="showWorkflowDialog = true"
              :disabled="instances.length === 0"
            >
              <el-icon><Plus /></el-icon>
              添加工作流
            </el-button>
            <el-button @click="refreshWorkflows">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <div class="filters">
              <el-select
                v-model="selectedInstanceId"
                @change="loadWorkflows"
                placeholder="选择实例"
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="instance in instances"
                  :key="instance.id"
                  :label="instance.name"
                  :value="instance.id"
                />
              </el-select>
            </div>
          </div>

          <el-table 
            :data="workflows" 
            v-loading="workflowsLoading"
            class="workflows-table"
          >
            <el-table-column prop="name" label="工作流名称" min-width="120" />
            <el-table-column prop="workflow_id" label="工作流ID" min-width="150" />
            <el-table-column prop="workflow_type" label="类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getWorkflowTypeColor(row.workflow_type)">
                  {{ getWorkflowTypeText(row.workflow_type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" min-width="150">
              <template #default="{ row }">
                <el-text truncated>{{ row.description || '-' }}</el-text>
              </template>
            </el-table-column>
            <el-table-column prop="enabled" label="状态" width="80">
              <template #default="{ row }">
                <el-switch
                  v-model="row.enabled"
                  @change="toggleWorkflowStatus(row)"
                  :disabled="workflowsLoading"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button-group>
                  <el-button 
                    size="small" 
                    @click="testWorkflow(row)"
                  >
                    测试
                  </el-button>
                  <el-button 
                    size="small" 
                    @click="editWorkflow(row)"
                  >
                    编辑
                  </el-button>
                  <el-button 
                    size="small" 
                    type="danger" 
                    @click="deleteWorkflow(row)"
                  >
                    删除
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 执行记录 -->
      <el-tab-pane label="执行记录" name="executions">
        <div class="tab-content">
          <div class="toolbar">
            <el-button @click="refreshExecutions">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <div class="filters">
              <el-select
                v-model="executionFilters.instance_id"
                @change="loadExecutions"
                placeholder="选择实例"
                clearable
                style="width: 150px; margin-right: 10px"
              >
                <el-option
                  v-for="instance in instances"
                  :key="instance.id"
                  :label="instance.name"
                  :value="instance.id"
                />
              </el-select>
              <el-select
                v-model="executionFilters.workflow_id"
                @change="loadExecutions"
                placeholder="选择工作流"
                clearable
                style="width: 150px"
              >
                <el-option
                  v-for="workflow in workflows"
                  :key="workflow.id"
                  :label="workflow.name"
                  :value="workflow.workflow_id"
                />
              </el-select>
            </div>
          </div>

          <el-table 
            :data="executions" 
            v-loading="executionsLoading"
            class="executions-table"
          >
            <el-table-column prop="execution_id" label="执行ID" width="120">
              <template #default="{ row }">
                <el-text class="execution-id" truncated>
                  {{ row.execution_id.substring(0, 8) }}...
                </el-text>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getExecutionStatusType(row.status)">
                  {{ getExecutionStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="started_at" label="开始时间" width="150">
              <template #default="{ row }">
                <el-text size="small">{{ formatTime(row.started_at) }}</el-text>
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="耗时" width="80">
              <template #default="{ row }">
                <el-text size="small">
                  {{ row.duration ? `${row.duration.toFixed(2)}s` : '-' }}
                </el-text>
              </template>
            </el-table-column>
            <el-table-column prop="error_message" label="错误信息" min-width="200">
              <template #default="{ row }">
                <el-text 
                  v-if="row.error_message" 
                  type="danger" 
                  truncated
                >
                  {{ row.error_message }}
                </el-text>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" fixed="right">
              <template #default="{ row }">
                <el-button 
                  size="small" 
                  @click="viewExecutionDetail(row)"
                >
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 工作流模板 -->
      <el-tab-pane label="工作流模板" name="templates">
        <div class="tab-content">
          <div class="toolbar">
            <el-button @click="refreshTemplates">
              <el-icon><Refresh /></el-icon>
              刷新模板
            </el-button>
            <div class="filters">
              <el-switch
                v-model="showDifyTemplatesOnly"
                @change="filterTemplates"
                active-text="仅显示Dify集成"
                inactive-text="显示全部"
              />
            </div>
          </div>

          <el-table
            :data="filteredTemplates"
            v-loading="templatesLoading"
            class="templates-table"
          >
            <el-table-column prop="name" label="模板名称" min-width="150">
              <template #default="{ row }">
                <div class="template-name">
                  <span>{{ row.name }}</span>
                  <el-tag
                    v-if="row.has_dify_integration"
                    type="success"
                    size="small"
                    class="dify-tag"
                  >
                    Dify集成
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
            <el-table-column prop="version" label="版本" width="80" />
            <el-table-column prop="step_count" label="步骤数" width="80" />
            <el-table-column label="标签" width="150">
              <template #default="{ row }">
                <el-tag
                  v-for="tag in row.tags.slice(0, 2)"
                  :key="tag"
                  size="small"
                  class="tag-item"
                >
                  {{ tag }}
                </el-tag>
                <span v-if="row.tags.length > 2" class="more-tags">
                  +{{ row.tags.length - 2 }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button
                  size="small"
                  @click="viewTemplateDetail(row)"
                >
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 实例配置对话框 -->
    <InstanceDialog
      v-model="showInstanceDialog"
      :instance="currentInstance"
      @saved="handleInstanceSaved"
    />

    <!-- 工作流配置对话框 -->
    <WorkflowDialog
      v-model="showWorkflowDialog"
      :workflow="currentWorkflow"
      :instances="instances"
      @saved="handleWorkflowSaved"
    />

    <!-- 执行详情对话框 -->
    <ExecutionDetailDialog
      v-model="showExecutionDialog"
      :execution="currentExecution"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import difyApi, { type DifyInstance, type DifyWorkflow, type DifyExecutionLog } from '@/api/dify'
import InstanceDialog from './components/InstanceDialog.vue'
import WorkflowDialog from './components/WorkflowDialog.vue'
import ExecutionDetailDialog from './components/ExecutionDetailDialog.vue'

// 响应式数据
const activeTab = ref('instances')
const showEnabledOnly = ref(false)
const selectedInstanceId = ref('')

// 实例相关
const instances = ref<DifyInstance[]>([])
const instancesLoading = ref(false)
const testingInstances = ref(new Set<string>())
const showInstanceDialog = ref(false)
const currentInstance = ref<DifyInstance | null>(null)

// 工作流相关
const workflows = ref<DifyWorkflow[]>([])
const workflowsLoading = ref(false)
const showWorkflowDialog = ref(false)
const currentWorkflow = ref<DifyWorkflow | null>(null)

// 执行记录相关
const executions = ref<DifyExecutionLog[]>([])
const executionsLoading = ref(false)
const showExecutionDialog = ref(false)
const currentExecution = ref<DifyExecutionLog | null>(null)
const executionFilters = reactive({
  instance_id: '',
  workflow_id: '',
  limit: 100
})

// 模板相关
const templates = ref<any[]>([])
const templatesLoading = ref(false)
const showDifyTemplatesOnly = ref(false)
const filteredTemplates = computed(() => {
  if (showDifyTemplatesOnly.value) {
    return templates.value.filter(t => t.has_dify_integration)
  }
  return templates.value
})

// 工具函数
const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    active: 'success',
    inactive: 'info',
    error: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    active: '正常',
    inactive: '未激活',
    error: '错误'
  }
  return texts[status] || '未知'
}

const getWorkflowTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    chatbot: 'primary',
    workflow: 'success',
    agent: 'warning',
    completion: 'info'
  }
  return colors[type] || 'info'
}

const getWorkflowTypeText = (type: string) => {
  const texts: Record<string, string> = {
    chatbot: '聊天机器人',
    workflow: '工作流',
    agent: '智能体',
    completion: '文本补全'
  }
  return texts[type] || type
}

const getExecutionStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  return types[status] || 'info'
}

const getExecutionStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return texts[status] || status
}

// 数据加载方法
const loadInstances = async () => {
  instancesLoading.value = true
  try {
    const response = await difyApi.instance.list({ 
      enabled_only: showEnabledOnly.value 
    })
    instances.value = response.data.data
  } catch (error) {
    ElMessage.error('加载实例列表失败')
    console.error(error)
  } finally {
    instancesLoading.value = false
  }
}

const loadWorkflows = async () => {
  workflowsLoading.value = true
  try {
    const response = await difyApi.workflow.list({
      instance_id: selectedInstanceId.value || undefined
    })
    workflows.value = response.data.data
  } catch (error) {
    ElMessage.error('加载工作流列表失败')
    console.error(error)
  } finally {
    workflowsLoading.value = false
  }
}

const loadExecutions = async () => {
  executionsLoading.value = true
  try {
    const response = await difyApi.execution.list(executionFilters)
    executions.value = response.data.data
  } catch (error) {
    ElMessage.error('加载执行记录失败')
    console.error(error)
  } finally {
    executionsLoading.value = false
  }
}

// 刷新方法
const refreshInstances = () => loadInstances()
const refreshWorkflows = () => loadWorkflows()
const refreshExecutions = () => loadExecutions()

// 实例操作方法
const testInstance = async (instance: DifyInstance) => {
  if (!instance.id) return
  
  testingInstances.value.add(instance.id)
  try {
    const response = await difyApi.instance.test({ instance_id: instance.id })
    if (response.data.success) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.error(`连接测试失败: ${response.data.message}`)
    }
    // 刷新实例列表以更新状态
    await loadInstances()
  } catch (error) {
    ElMessage.error('连接测试失败')
    console.error(error)
  } finally {
    testingInstances.value.delete(instance.id!)
  }
}

const editInstance = (instance: DifyInstance) => {
  currentInstance.value = { ...instance }
  showInstanceDialog.value = true
}

const deleteInstance = async (instance: DifyInstance) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除实例 "${instance.name}" 吗？此操作将同时删除相关的工作流配置。`,
      '确认删除',
      { type: 'warning' }
    )
    
    await difyApi.instance.delete(instance.id!)
    ElMessage.success('删除成功')
    await loadInstances()
    await loadWorkflows() // 刷新工作流列表
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error(error)
    }
  }
}

const toggleInstanceStatus = async (instance: DifyInstance) => {
  try {
    await difyApi.instance.update(instance.id!, instance)
    ElMessage.success('状态更新成功')
  } catch (error) {
    // 恢复原状态
    instance.enabled = !instance.enabled
    ElMessage.error('状态更新失败')
    console.error(error)
  }
}

// 工作流操作方法
const editWorkflow = (workflow: DifyWorkflow) => {
  currentWorkflow.value = { ...workflow }
  showWorkflowDialog.value = true
}

const deleteWorkflow = async (workflow: DifyWorkflow) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除工作流 "${workflow.name}" 吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    await difyApi.workflow.delete(workflow.id!)
    ElMessage.success('删除成功')
    await loadWorkflows()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error(error)
    }
  }
}

const toggleWorkflowStatus = async (workflow: DifyWorkflow) => {
  try {
    await difyApi.workflow.update(workflow.id!, workflow)
    ElMessage.success('状态更新成功')
  } catch (error) {
    // 恢复原状态
    workflow.enabled = !workflow.enabled
    ElMessage.error('状态更新失败')
    console.error(error)
  }
}

const testWorkflow = (workflow: DifyWorkflow) => {
  ElMessage.info('工作流测试功能开发中...')
}

// 执行记录操作方法
const viewExecutionDetail = (execution: DifyExecutionLog) => {
  currentExecution.value = execution
  showExecutionDialog.value = true
}

// 模板相关方法
const loadTemplates = async () => {
  templatesLoading.value = true
  try {
    const response = await fetch('/api/v1/dify/workflow-templates')
    const result = await response.json()
    if (result.success) {
      templates.value = result.data
    } else {
      ElMessage.error('加载工作流模板失败')
    }
  } catch (error) {
    ElMessage.error('加载工作流模板失败')
    console.error(error)
  } finally {
    templatesLoading.value = false
  }
}

const refreshTemplates = () => loadTemplates()

const filterTemplates = () => {
  // 过滤逻辑已在computed中处理
}

const viewTemplateDetail = async (template: any) => {
  try {
    const response = await fetch(`/api/v1/dify/workflow-templates/${template.id}`)
    const result = await response.json()
    if (result.success) {
      // 显示模板详情对话框
      ElMessageBox.alert(
        `<pre>${JSON.stringify(result.data, null, 2)}</pre>`,
        `工作流模板详情: ${template.name}`,
        {
          dangerouslyUseHTMLString: true,
          customClass: 'template-detail-dialog'
        }
      )
    } else {
      ElMessage.error('获取模板详情失败')
    }
  } catch (error) {
    ElMessage.error('获取模板详情失败')
    console.error(error)
  }
}

// 对话框事件处理
const handleInstanceSaved = () => {
  showInstanceDialog.value = false
  currentInstance.value = null
  loadInstances()
}

const handleWorkflowSaved = () => {
  showWorkflowDialog.value = false
  currentWorkflow.value = null
  loadWorkflows()
}

// 初始化
onMounted(() => {
  loadInstances()
  loadWorkflows()
  loadExecutions()
  loadTemplates()
})
</script>

<style scoped>
.dify-management {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}

.header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.dify-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.tab-content {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.filters {
  display: flex;
  align-items: center;
  gap: 12px;
}

.instance-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-tag {
  font-size: 12px;
}

.url-text {
  font-family: monospace;
  font-size: 12px;
}

.execution-id {
  font-family: monospace;
  font-size: 12px;
}

.instances-table,
.workflows-table,
.executions-table,
.templates-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.template-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dify-tag {
  font-size: 12px;
  background-color: #67c23a;
  border-color: #67c23a;
}

.tag-item {
  margin-right: 4px;
  margin-bottom: 4px;
}

.more-tags {
  font-size: 12px;
  color: #909399;
}

:deep(.el-table__header) {
  background-color: #fafafa;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

:deep(.el-button-group .el-button) {
  margin: 0;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.template-detail-dialog) {
  width: 80%;
  max-width: 1000px;
}

:deep(.template-detail-dialog .el-message-box__content) {
  max-height: 600px;
  overflow-y: auto;
}

:deep(.template-detail-dialog pre) {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
