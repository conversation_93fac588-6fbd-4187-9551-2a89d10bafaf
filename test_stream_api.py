#!/usr/bin/env python3
"""
测试AI角色管理流式调用功能
"""

import asyncio
import aiohttp
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_stream_api():
    """测试流式API"""
    
    # 测试数据
    test_data = {
        "user_input": "你好，请介绍一下你自己",
        "context": {}
    }
    
    # 这里需要替换为实际的角色实例ID
    # 您可以从数据库中获取一个真实的实例ID，或者通过API创建一个
    instance_id = "请替换为真实的角色实例ID"
    
    url = f"http://localhost:8000/api/v1/system-config/ai-role-instances/{instance_id}/use-stream"
    
    print("开始测试流式API...")
    print(f"URL: {url}")
    print(f"数据: {test_data}")
    print("-" * 50)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=test_data) as response:
                print(f"响应状态: {response.status}")
                
                if response.status != 200:
                    error_text = await response.text()
                    print(f"错误响应: {error_text}")
                    return
                
                print("开始接收流式数据:")
                print("-" * 30)
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    
                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])
                            
                            if 'error' in data:
                                print(f"❌ 错误: {data['error']}")
                                break
                            elif 'content' in data:
                                print(f"📝 内容: {data['content']}", end='', flush=True)
                            elif 'done' in data and data['done']:
                                print("\n✅ 流式响应完成")
                                break
                                
                        except json.JSONDecodeError as e:
                            print(f"⚠️ JSON解析错误: {e}")
                            continue
                
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

async def test_normal_api():
    """测试普通API作为对比"""
    
    # 测试数据
    test_data = {
        "user_input": "你好，请介绍一下你自己",
        "context": {}
    }
    
    # 这里需要替换为实际的角色实例ID
    instance_id = "test_instance_id"
    
    url = f"http://localhost:8000/api/v1/system-config/ai-role-instances/{instance_id}/use"
    
    print("\n" + "=" * 50)
    print("开始测试普通API...")
    print(f"URL: {url}")
    print(f"数据: {test_data}")
    print("-" * 50)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=test_data) as response:
                print(f"响应状态: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 响应成功:")
                    print(f"   内容: {result.get('response', 'N/A')}")
                    print(f"   实例名: {result.get('instance_name', 'N/A')}")
                    print(f"   模板名: {result.get('template_name', 'N/A')}")
                    print(f"   提供商: {result.get('provider_name', 'N/A')}")
                else:
                    error_text = await response.text()
                    print(f"❌ 错误响应: {error_text}")
                
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 AI角色管理流式调用测试")
    print("=" * 50)
    
    # 运行测试
    asyncio.run(test_stream_api())
    asyncio.run(test_normal_api())
    
    print("\n" + "=" * 50)
    print("📋 测试说明:")
    print("1. 请确保后端服务正在运行 (http://localhost:8000)")
    print("2. 请将 instance_id 替换为实际的AI角色实例ID")
    print("3. 请确保AI提供商已正确配置")
    print("4. 流式API应该逐字符返回响应")
    print("5. 普通API应该一次性返回完整响应")
