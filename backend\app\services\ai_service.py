"""
AI服务模块
处理AI提供商调用、模版测试、角色实例使用等功能
"""

import logging
import asyncio
import aiohttp
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorDatabase

logger = logging.getLogger(__name__)


class AIService:
    """AI服务类"""
    
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db
    
    async def test_provider_connection(self, provider_id: str) -> Dict[str, Any]:
        """测试AI提供商连接"""
        try:
            from bson import ObjectId
            
            # 获取提供商信息
            provider = await self.db.ai_providers.find_one({"_id": ObjectId(provider_id)})
            if not provider:
                return {"success": False, "message": "AI提供商不存在"}
            
            # 根据提供商类型进行测试
            provider_type = provider.get("type", "").lower()
            
            if provider_type == "openai":
                return await self._test_openai_connection(provider)
            elif provider_type == "azure":
                return await self._test_azure_connection(provider)
            elif provider_type == "claude":
                return await self._test_claude_connection(provider)
            elif provider_type == "custom":
                return await self._test_custom_connection(provider)
            else:
                return {"success": False, "message": f"不支持的提供商类型: {provider_type}"}
                
        except Exception as e:
            logger.error(f"测试AI提供商连接失败: {str(e)}")
            return {"success": False, "message": f"连接测试失败: {str(e)}"}
    
    async def _test_openai_connection(self, provider: Dict[str, Any]) -> Dict[str, Any]:
        """测试OpenAI连接"""
        try:
            headers = {
                "Authorization": f"Bearer {provider['api_key']}",
                "Content-Type": "application/json"
            }
            
            # 使用简单的模型列表API测试连接
            test_url = f"{provider['endpoint'].rstrip('/')}/v1/models"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(test_url, headers=headers, timeout=10) as response:
                    if response.status == 200:
                        return {"success": True, "message": "OpenAI连接测试成功"}
                    else:
                        error_text = await response.text()
                        return {"success": False, "message": f"OpenAI连接失败: {error_text}"}
                        
        except asyncio.TimeoutError:
            return {"success": False, "message": "连接超时"}
        except Exception as e:
            return {"success": False, "message": f"OpenAI连接测试失败: {str(e)}"}
    
    async def _test_azure_connection(self, provider: Dict[str, Any]) -> Dict[str, Any]:
        """测试Azure OpenAI连接"""
        try:
            headers = {
                "api-key": provider['api_key'],
                "Content-Type": "application/json"
            }
            
            # Azure OpenAI的测试端点
            test_url = f"{provider['endpoint'].rstrip('/')}/openai/deployments?api-version=2023-05-15"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(test_url, headers=headers, timeout=10) as response:
                    if response.status == 200:
                        return {"success": True, "message": "Azure OpenAI连接测试成功"}
                    else:
                        error_text = await response.text()
                        return {"success": False, "message": f"Azure OpenAI连接失败: {error_text}"}
                        
        except asyncio.TimeoutError:
            return {"success": False, "message": "连接超时"}
        except Exception as e:
            return {"success": False, "message": f"Azure OpenAI连接测试失败: {str(e)}"}
    
    async def _test_claude_connection(self, provider: Dict[str, Any]) -> Dict[str, Any]:
        """测试Claude连接"""
        try:
            headers = {
                "x-api-key": provider['api_key'],
                "Content-Type": "application/json",
                "anthropic-version": "2023-06-01"
            }
            
            # 使用简单的消息API测试连接
            test_url = f"{provider['endpoint'].rstrip('/')}/v1/messages"
            test_data = {
                "model": provider.get('model', 'claude-3-sonnet-20240229'),
                "max_tokens": 10,
                "messages": [{"role": "user", "content": "Hello"}]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(test_url, headers=headers, json=test_data, timeout=10) as response:
                    if response.status == 200:
                        return {"success": True, "message": "Claude连接测试成功"}
                    else:
                        error_text = await response.text()
                        return {"success": False, "message": f"Claude连接失败: {error_text}"}
                        
        except asyncio.TimeoutError:
            return {"success": False, "message": "连接超时"}
        except Exception as e:
            return {"success": False, "message": f"Claude连接测试失败: {str(e)}"}
    
    async def _test_custom_connection(self, provider: Dict[str, Any]) -> Dict[str, Any]:
        """测试自定义提供商连接"""
        try:
            # 对于自定义提供商，尝试简单的HTTP请求
            async with aiohttp.ClientSession() as session:
                async with session.get(provider['endpoint'], timeout=10) as response:
                    if response.status < 500:  # 只要不是服务器错误就认为连接成功
                        return {"success": True, "message": "自定义提供商连接测试成功"}
                    else:
                        return {"success": False, "message": f"自定义提供商连接失败: HTTP {response.status}"}
                        
        except asyncio.TimeoutError:
            return {"success": False, "message": "连接超时"}
        except Exception as e:
            return {"success": False, "message": f"自定义提供商连接测试失败: {str(e)}"}
    
    async def test_role_template(self, template_id: str, test_input: str) -> Dict[str, Any]:
        """测试AI角色模版"""
        try:
            from bson import ObjectId
            
            # 获取模版信息
            template = await self.db.ai_role_templates.find_one({"_id": ObjectId(template_id)})
            if not template:
                return {"success": False, "message": "AI角色模版不存在"}
            
            # 查找可用的AI提供商（优先选择启用的）
            provider = await self.db.ai_providers.find_one({"enabled": True})
            if not provider:
                # 如果没有启用的提供商，查找任何提供商用于测试
                provider = await self.db.ai_providers.find_one({})
                if not provider:
                    return {"success": False, "message": "系统中没有配置任何AI提供商，请先在系统配置中添加AI提供商"}
                else:
                    return {"success": False, "message": f"没有启用的AI提供商，请在系统配置中启用AI提供商并配置正确的API密钥。当前找到提供商: {provider.get('name', '未知')}"}
            
            # 构建完整的提示词
            system_prompt = template.get("system_prompt", "")
            user_prompt_template = template.get("user_prompt_template", "")
            
            # 简单的变量替换（实际应用中可能需要更复杂的模版引擎）
            user_prompt = user_prompt_template.replace("{test_input}", test_input)
            
            # 调用AI提供商
            response = await self._call_ai_provider(provider, system_prompt, user_prompt)
            
            if response["success"]:
                return {
                    "success": True,
                    "response": response["content"],
                    "template_name": template.get("name", ""),
                    "provider_name": provider.get("name", "")
                }
            else:
                return {
                    "success": False,
                    "message": f"AI调用失败: {response['error']}"
                }
                
        except Exception as e:
            logger.error(f"测试AI角色模版失败: {str(e)}")
            return {"success": False, "message": f"测试失败: {str(e)}"}
    
    async def _call_ai_provider(self, provider: Dict[str, Any], system_prompt: str, user_prompt: str, stream: bool = False) -> Dict[str, Any]:
        """调用AI提供商"""
        try:
            provider_type = provider.get("type", "").lower()

            if provider_type == "openai":
                return await self._call_openai(provider, system_prompt, user_prompt, stream)
            elif provider_type == "azure":
                return await self._call_azure_openai(provider, system_prompt, user_prompt, stream)
            elif provider_type == "claude":
                return await self._call_claude(provider, system_prompt, user_prompt, stream)
            elif provider_type == "custom":
                return await self._call_custom_provider(provider, system_prompt, user_prompt, stream)
            else:
                return {"success": False, "error": f"不支持的提供商类型: {provider_type}"}

        except Exception as e:
            logger.error(f"调用AI提供商失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _call_openai(self, provider: Dict[str, Any], system_prompt: str, user_prompt: str, stream: bool = False) -> Dict[str, Any]:
        """调用OpenAI API"""
        try:
            api_key = provider.get('api_key', '')

            # 检查是否是默认的API密钥
            if api_key in ['your-openai-api-key-here', 'demo-key-for-testing']:
                return {"success": False, "error": "请先配置真实的OpenAI API密钥，当前使用的是默认密钥"}

            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": user_prompt})

            data = {
                "model": provider.get("model", "gpt-3.5-turbo"),
                "messages": messages,
                "max_tokens": provider.get("max_tokens", 2000),
                "temperature": provider.get("temperature", 0.7),
                "stream": stream
            }
            
            url = f"{provider['endpoint'].rstrip('/')}/v1/chat/completions"

            if stream:
                return {"success": True, "stream_url": url, "headers": headers, "data": data}

            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data, timeout=30) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result["choices"][0]["message"]["content"]
                        return {"success": True, "content": content}
                    else:
                        error_text = await response.text()
                        return {"success": False, "error": f"OpenAI API错误: {error_text}"}

        except Exception as e:
            return {"success": False, "error": f"OpenAI调用失败: {str(e)}"}
    
    async def _call_azure_openai(self, provider: Dict[str, Any], system_prompt: str, user_prompt: str, stream: bool = False) -> Dict[str, Any]:
        """调用Azure OpenAI API"""
        try:
            api_key = provider.get('api_key', '')

            # 检查是否是默认的API密钥
            if api_key in ['your-azure-api-key-here', 'demo-key-for-testing']:
                return {"success": False, "error": "请先配置真实的Azure OpenAI API密钥，当前使用的是默认密钥"}

            headers = {
                "api-key": api_key,
                "Content-Type": "application/json"
            }
            
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": user_prompt})
            
            data = {
                "messages": messages,
                "max_tokens": provider.get("max_tokens", 2000),
                "temperature": provider.get("temperature", 0.7)
            }
            
            # Azure OpenAI需要部署名称
            deployment_name = provider.get("model", "gpt-35-turbo")
            url = f"{provider['endpoint'].rstrip('/')}/openai/deployments/{deployment_name}/chat/completions?api-version=2023-05-15"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data, timeout=30) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result["choices"][0]["message"]["content"]
                        return {"success": True, "content": content}
                    else:
                        error_text = await response.text()
                        return {"success": False, "error": f"Azure OpenAI API错误: {error_text}"}
                        
        except Exception as e:
            return {"success": False, "error": f"Azure OpenAI调用失败: {str(e)}"}
    
    async def _call_claude(self, provider: Dict[str, Any], system_prompt: str, user_prompt: str, stream: bool = False) -> Dict[str, Any]:
        """调用Claude API"""
        try:
            api_key = provider.get('api_key', '')

            # 检查是否是默认的API密钥
            if api_key in ['your-claude-api-key-here', 'demo-key-for-testing']:
                return {"success": False, "error": "请先配置真实的Claude API密钥，当前使用的是默认密钥"}

            headers = {
                "x-api-key": api_key,
                "Content-Type": "application/json",
                "anthropic-version": "2023-06-01"
            }
            
            data = {
                "model": provider.get("model", "claude-3-sonnet-20240229"),
                "max_tokens": provider.get("max_tokens", 2000),
                "messages": [{"role": "user", "content": user_prompt}]
            }
            
            if system_prompt:
                data["system"] = system_prompt
            
            url = f"{provider['endpoint'].rstrip('/')}/v1/messages"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data, timeout=30) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result["content"][0]["text"]
                        return {"success": True, "content": content}
                    else:
                        error_text = await response.text()
                        return {"success": False, "error": f"Claude API错误: {error_text}"}
                        
        except Exception as e:
            return {"success": False, "error": f"Claude调用失败: {str(e)}"}

    async def use_role_instance_stream(self, instance_id: str, user_input: str, context: Optional[Dict[str, Any]] = None):
        """流式使用AI角色实例"""
        try:
            from bson import ObjectId

            logger.info(f"🚀 开始流式调用AI角色实例: {instance_id}")

            # 获取角色实例信息
            instance = await self.db.ai_role_instances.find_one({"_id": ObjectId(instance_id)})
            if not instance:
                logger.error(f"❌ AI角色实例不存在: {instance_id}")
                yield {"error": "AI角色实例不存在"}
                return

            if not instance.get("enabled", True):
                yield {"error": "AI角色实例已禁用"}
                return

            # 获取关联的模版
            template = await self.db.ai_role_templates.find_one({"_id": ObjectId(instance["template_id"])})
            if not template:
                yield {"error": "关联的AI角色模版不存在"}
                return

            # 获取关联的提供商
            provider = await self.db.ai_providers.find_one({"_id": ObjectId(instance["provider_id"])})
            if not provider:
                logger.error(f"❌ 关联的AI提供商不存在: {instance['provider_id']}")
                yield {"error": "关联的AI提供商不存在"}
                return

            if not provider.get("enabled", True):
                logger.error(f"❌ 关联的AI提供商已禁用: {provider.get('name', 'Unknown')}")
                yield {"error": "关联的AI提供商已禁用"}
                return

            logger.info(f"📡 使用提供商: {provider.get('name', 'Unknown')} (类型: {provider.get('type', 'Unknown')})")

            # 构建完整的提示词
            system_prompt = template.get("system_prompt", "")
            user_prompt_template = template.get("user_prompt_template", "")

            # 处理用户输入（简单的变量替换）
            user_prompt = user_prompt_template.replace("{user_input}", user_input)

            # 如果有上下文信息，也进行替换
            if context:
                for key, value in context.items():
                    user_prompt = user_prompt.replace(f"{{{key}}}", str(value))

            # 流式调用AI提供商
            async for chunk in self._call_ai_provider_stream(provider, system_prompt, user_prompt):
                yield chunk

            # 更新使用统计
            await self.db.ai_role_instances.update_one(
                {"_id": ObjectId(instance_id)},
                {
                    "$inc": {"usage_count": 1},
                    "$set": {"last_used_at": datetime.utcnow()}
                }
            )

        except Exception as e:
            logger.error(f"流式使用AI角色实例失败: {str(e)}")
            yield {"error": f"调用失败: {str(e)}"}

    async def _call_ai_provider_stream(self, provider: Dict[str, Any], system_prompt: str, user_prompt: str):
        """流式调用AI提供商"""
        try:
            provider_type = provider.get("type", "").lower()

            if provider_type == "openai":
                async for chunk in self._call_openai_stream(provider, system_prompt, user_prompt):
                    yield chunk
            elif provider_type == "claude":
                async for chunk in self._call_claude_stream(provider, system_prompt, user_prompt):
                    yield chunk
            elif provider_type == "custom":
                async for chunk in self._call_custom_provider_stream(provider, system_prompt, user_prompt):
                    yield chunk
            else:
                yield {"error": f"提供商类型 {provider_type} 暂不支持流式调用"}

        except Exception as e:
            logger.error(f"流式调用AI提供商失败: {str(e)}")
            yield {"error": f"调用失败: {str(e)}"}

    async def _call_openai_stream(self, provider: Dict[str, Any], system_prompt: str, user_prompt: str):
        """流式调用OpenAI API"""
        try:
            api_key = provider.get('api_key', '')

            # 检查是否是默认的API密钥
            if api_key in ['your-openai-api-key-here', 'demo-key-for-testing']:
                yield {"error": "请先配置真实的OpenAI API密钥，当前使用的是默认密钥"}
                return

            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": user_prompt})

            data = {
                "model": provider.get("model", "gpt-3.5-turbo"),
                "messages": messages,
                "max_tokens": provider.get("max_tokens", 2000),
                "temperature": provider.get("temperature", 0.7),
                "stream": True
            }

            url = f"{provider['endpoint'].rstrip('/')}/v1/chat/completions"

            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        yield {"error": f"OpenAI API错误: {error_text}"}
                        return

                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        if line.startswith('data: '):
                            if line == 'data: [DONE]':
                                break
                            try:
                                import json
                                data = json.loads(line[6:])
                                if 'choices' in data and len(data['choices']) > 0:
                                    delta = data['choices'][0].get('delta', {})
                                    if 'content' in delta:
                                        yield {"content": delta['content']}
                            except json.JSONDecodeError:
                                continue

        except Exception as e:
            yield {"error": f"OpenAI流式调用失败: {str(e)}"}

    async def _call_claude_stream(self, provider: Dict[str, Any], system_prompt: str, user_prompt: str):
        """流式调用Claude API"""
        try:
            api_key = provider.get('api_key', '')

            # 检查是否是默认的API密钥
            if api_key in ['your-claude-api-key-here', 'demo-key-for-testing']:
                yield {"error": "请先配置真实的Claude API密钥，当前使用的是默认密钥"}
                return

            headers = {
                "x-api-key": api_key,
                "Content-Type": "application/json",
                "anthropic-version": "2023-06-01"
            }

            data = {
                "model": provider.get("model", "claude-3-sonnet-20240229"),
                "max_tokens": provider.get("max_tokens", 2000),
                "messages": [{"role": "user", "content": user_prompt}],
                "stream": True
            }

            if system_prompt:
                data["system"] = system_prompt

            url = f"{provider['endpoint'].rstrip('/')}/v1/messages"

            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        yield {"error": f"Claude API错误: {error_text}"}
                        return

                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        if line.startswith('data: '):
                            try:
                                import json
                                data = json.loads(line[6:])
                                if data.get('type') == 'content_block_delta':
                                    delta = data.get('delta', {})
                                    if 'text' in delta:
                                        yield {"content": delta['text']}
                            except json.JSONDecodeError:
                                continue

        except Exception as e:
            yield {"error": f"Claude流式调用失败: {str(e)}"}

    async def _call_custom_provider_stream(self, provider: Dict[str, Any], system_prompt: str, user_prompt: str):
        """流式调用自定义提供商API"""
        try:
            api_key = provider.get('api_key', '')

            # 检查是否是默认的演示API密钥
            if api_key in ['demo-key-for-testing', 'your-openai-api-key-here', 'your-azure-api-key-here', 'your-claude-api-key-here']:
                yield {"error": f"请先配置真实的API密钥。当前使用的是默认密钥: {api_key}，请在系统配置中更新AI提供商的API密钥"}
                return

            # 对于自定义提供商，尝试使用OpenAI兼容的API格式
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": user_prompt})

            data = {
                "model": provider.get("model", "gpt-3.5-turbo"),
                "messages": messages,
                "max_tokens": provider.get("max_tokens", 2000),
                "temperature": provider.get("temperature", 0.7),
                "stream": True
            }

            # 构建URL，避免重复路径
            endpoint = provider['endpoint'].rstrip('/')
            # 如果endpoint已经包含了API路径，就不要再添加
            if endpoint.endswith('/v1') or endpoint.endswith('/v1/chat/completions'):
                if endpoint.endswith('/v1/chat/completions'):
                    url = endpoint
                else:
                    url = f"{endpoint}/chat/completions"
            else:
                url = f"{endpoint}/v1/chat/completions"

            logger.info(f"流式调用自定义提供商API: {url}")

            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        yield {"error": f"自定义提供商API错误: HTTP {response.status} - {error_text}"}
                        return

                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        if line.startswith('data: '):
                            if line == 'data: [DONE]':
                                break
                            try:
                                import json
                                data = json.loads(line[6:])
                                if 'choices' in data and len(data['choices']) > 0:
                                    delta = data['choices'][0].get('delta', {})
                                    if 'content' in delta:
                                        yield {"content": delta['content']}
                            except json.JSONDecodeError:
                                continue

        except Exception as e:
            yield {"error": f"自定义提供商流式调用失败: {str(e)}"}

    async def _call_custom_provider(self, provider: Dict[str, Any], system_prompt: str, user_prompt: str, stream: bool = False) -> Dict[str, Any]:
        """调用自定义提供商API"""
        try:
            api_key = provider.get('api_key', '')

            # 检查是否是默认的演示API密钥
            if api_key in ['demo-key-for-testing', 'your-openai-api-key-here', 'your-azure-api-key-here', 'your-claude-api-key-here']:
                return {"success": False, "error": f"请先配置真实的API密钥。当前使用的是默认密钥: {api_key}，请在系统配置中更新AI提供商的API密钥"}

            # 对于自定义提供商，尝试使用OpenAI兼容的API格式
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": user_prompt})

            data = {
                "model": provider.get("model", "gpt-3.5-turbo"),
                "messages": messages,
                "max_tokens": provider.get("max_tokens", 2000),
                "temperature": provider.get("temperature", 0.7),
                "stream": stream
            }

            # 构建URL，避免重复路径
            endpoint = provider['endpoint'].rstrip('/')
            # 如果endpoint已经包含了API路径，就不要再添加
            if endpoint.endswith('/v1') or endpoint.endswith('/v1/chat/completions'):
                if endpoint.endswith('/v1/chat/completions'):
                    url = endpoint
                else:
                    url = f"{endpoint}/chat/completions"
            else:
                url = f"{endpoint}/v1/chat/completions"

            logger.info(f"调用自定义提供商API: {url}")
            logger.info(f"📤 请求数据: {data}")
            logger.info(f"🔧 Stream参数: {stream}")

            if stream:
                return {"success": True, "stream_url": url, "headers": headers, "data": data}

            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data, timeout=30) as response:
                    if response.status == 200:
                        result = await response.json()
                        if "choices" in result and len(result["choices"]) > 0:
                            content = result["choices"][0]["message"]["content"]
                            return {"success": True, "content": content}
                        else:
                            return {"success": False, "error": "自定义提供商返回格式不正确"}
                    else:
                        error_text = await response.text()
                        return {"success": False, "error": f"自定义提供商API错误: HTTP {response.status} - {error_text}"}

        except Exception as e:
            return {"success": False, "error": f"自定义提供商调用失败: {str(e)}"}

    async def use_role_instance(self, instance_id: str, user_input: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """使用AI角色实例"""
        try:
            from bson import ObjectId

            logger.warning(f"⚠️ 非流式AI服务被调用！实例ID: {instance_id}")
            logger.warning(f"⚠️ 用户输入: {user_input[:100]}...")

            # 获取角色实例信息
            instance = await self.db.ai_role_instances.find_one({"_id": ObjectId(instance_id)})
            if not instance:
                return {"success": False, "message": "AI角色实例不存在"}
            
            if not instance.get("enabled", True):
                return {"success": False, "message": "AI角色实例已禁用"}
            
            # 获取关联的模版
            template = await self.db.ai_role_templates.find_one({"_id": ObjectId(instance["template_id"])})
            if not template:
                return {"success": False, "message": "关联的AI角色模版不存在"}
            
            # 获取关联的提供商
            provider = await self.db.ai_providers.find_one({"_id": ObjectId(instance["provider_id"])})
            if not provider:
                return {"success": False, "message": "关联的AI提供商不存在"}
            
            if not provider.get("enabled", True):
                return {"success": False, "message": "关联的AI提供商已禁用"}
            
            # 构建提示词
            system_prompt = template.get("system_prompt", "")
            user_prompt_template = template.get("user_prompt_template", "")
            
            # 添加角色个性化信息
            if instance.get("personality"):
                system_prompt += f"\n\n角色个性: {instance['personality']}"
            
            if instance.get("expertise"):
                system_prompt += f"\n专业领域: {', '.join(instance['expertise'])}"
            
            if instance.get("tone"):
                system_prompt += f"\n语调风格: {instance['tone']}"
            
            # 处理用户输入（简单的变量替换）
            user_prompt = user_prompt_template.replace("{user_input}", user_input)
            
            # 如果有上下文信息，也进行替换
            if context:
                for key, value in context.items():
                    user_prompt = user_prompt.replace(f"{{{key}}}", str(value))
            
            # 调用AI提供商
            response = await self._call_ai_provider(provider, system_prompt, user_prompt)
            
            if response["success"]:
                # 更新使用统计
                await self.db.ai_role_instances.update_one(
                    {"_id": ObjectId(instance_id)},
                    {
                        "$inc": {"usage_count": 1},
                        "$set": {"last_used_at": datetime.utcnow()}
                    }
                )
                
                return {
                    "success": True,
                    "response": response["content"],
                    "instance_name": instance.get("name", ""),
                    "template_name": template.get("name", ""),
                    "provider_name": provider.get("name", "")
                }
            else:
                return {
                    "success": False,
                    "message": f"AI调用失败: {response['error']}"
                }
                
        except Exception as e:
            logger.error(f"使用AI角色实例失败: {str(e)}")
            return {"success": False, "message": f"使用失败: {str(e)}"}
