/**
 * 系统配置相关API
 */
import request from '@/utils/request'

// AI提供商配置接口
export interface AIProvider {
  id: string
  name: string
  type: 'openai' | 'azure' | 'claude' | 'custom'
  endpoint: string
  api_key: string
  model: string
  max_tokens?: number
  temperature?: number
  enabled: boolean
  created_at: string
  updated_at: string
}

export interface CreateAIProviderRequest {
  name: string
  type: 'openai' | 'azure' | 'claude' | 'custom'
  endpoint: string
  api_key: string
  model: string
  max_tokens?: number
  temperature?: number
  enabled?: boolean
}

// AI角色模板接口
export interface AIRoleTemplate {
  id: string
  name: string
  description: string
  system_prompt: string
  user_prompt_template: string
  category: string
  tags: string[]
  enabled: boolean
  created_at: string
  updated_at: string
}

export interface CreateAIRoleTemplateRequest {
  name: string
  description: string
  system_prompt: string
  user_prompt_template: string
  category: string
  tags?: string[]
  enabled?: boolean
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface ListResponse<T> {
  items: T[]
  total: number
  page: number
  page_size: number
}

// AI提供商相关接口

/**
 * 获取AI提供商列表
 */
export const getAIProviders = (params?: {
  page?: number
  page_size?: number
  enabled?: boolean
}): Promise<ApiResponse<ListResponse<AIProvider>>> => {
  return request({
    url: '/api/v1/system-config/ai-providers',
    method: 'get',
    params
  })
}

/**
 * 获取AI提供商详情
 */
export const getAIProvider = (id: string): Promise<ApiResponse<AIProvider>> => {
  return request({
    url: `/api/v1/system-config/ai-providers/${id}`,
    method: 'get'
  })
}

/**
 * 创建AI提供商
 */
export const createAIProvider = (data: CreateAIProviderRequest): Promise<ApiResponse<AIProvider>> => {
  return request({
    url: '/api/v1/system-config/ai-providers',
    method: 'post',
    data
  })
}

/**
 * 更新AI提供商
 */
export const updateAIProvider = (id: string, data: Partial<CreateAIProviderRequest>): Promise<ApiResponse<AIProvider>> => {
  return request({
    url: `/api/v1/system-config/ai-providers/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除AI提供商
 */
export const deleteAIProvider = (id: string): Promise<ApiResponse<{ message: string }>> => {
  return request({
    url: `/api/v1/system-config/ai-providers/${id}`,
    method: 'delete'
  })
}

/**
 * 测试AI提供商连接
 */
export const testAIProvider = (id: string): Promise<ApiResponse<{ success: boolean; message: string }>> => {
  return request({
    url: `/api/v1/system-config/ai-providers/${id}/test`,
    method: 'post'
  })
}

// AI角色模板相关接口

/**
 * 获取AI角色模板列表
 */
export const getAIRoleTemplates = (params?: {
  page?: number
  page_size?: number
  category?: string
  enabled?: boolean
}): Promise<ApiResponse<ListResponse<AIRoleTemplate>>> => {
  return request({
    url: '/api/v1/system-config/ai-role-templates',
    method: 'get',
    params
  })
}

/**
 * 获取AI角色模板详情
 */
export const getAIRoleTemplate = (id: string): Promise<ApiResponse<AIRoleTemplate>> => {
  return request({
    url: `/api/v1/system-config/ai-role-templates/${id}`,
    method: 'get'
  })
}

/**
 * 创建AI角色模板
 */
export const createAIRoleTemplate = (data: CreateAIRoleTemplateRequest): Promise<ApiResponse<AIRoleTemplate>> => {
  return request({
    url: '/api/v1/system-config/ai-role-templates',
    method: 'post',
    data
  })
}

/**
 * 更新AI角色模板
 */
export const updateAIRoleTemplate = (id: string, data: Partial<CreateAIRoleTemplateRequest>): Promise<ApiResponse<AIRoleTemplate>> => {
  return request({
    url: `/api/v1/system-config/ai-role-templates/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除AI角色模板
 */
export const deleteAIRoleTemplate = (id: string): Promise<ApiResponse<{ message: string }>> => {
  return request({
    url: `/api/v1/system-config/ai-role-templates/${id}`,
    method: 'delete'
  })
}

/**
 * 获取模板分类列表
 */
export const getTemplateCategories = (): Promise<ApiResponse<string[]>> => {
  return request({
    url: '/api/v1/system-config/ai-role-templates/categories',
    method: 'get'
  })
}

/**
 * 测试AI角色模板
 */
export const testAIRoleTemplate = (id: string, test_input: string): Promise<ApiResponse<{ response: string }>> => {
  return request({
    url: `/api/v1/system-config/ai-role-templates/${id}/test`,
    method: 'post',
    data: { test_input }
  })
}

// ==================== AI角色实例相关API ====================

export interface AIRoleInstance {
  id: string
  name: string
  description: string
  template_id: string
  template_name?: string
  template_category?: string
  provider_id: string
  provider_name?: string
  provider_type?: string
  personality: string
  expertise: string[]
  tone: string
  workflow_types: string[]
  auto_suggestions: boolean
  memory_enabled: boolean
  context_window: number
  enabled: boolean
  usage_count: number
  created_at: string
  updated_at: string
  last_used_at?: string
}

export interface CreateAIRoleInstanceRequest {
  name: string
  description: string
  template_id: string
  provider_id: string
  model: string
  max_tokens?: number
  temperature?: number
  personality?: string
  expertise?: string[]
  tone?: string
  workflow_types?: string[]
  auto_suggestions?: boolean
  memory_enabled?: boolean
  context_window?: number
  enabled?: boolean
}

/**
 * 获取AI角色实例列表
 */
export const getAIRoleInstances = (params?: {
  page?: number
  page_size?: number
  enabled?: boolean
}): Promise<ApiResponse<ListResponse<AIRoleInstance>>> => {
  return request({
    url: '/api/v1/system-config/ai-role-instances',
    method: 'get',
    params
  })
}

/**
 * 创建AI角色实例
 */
export const createAIRoleInstance = (data: CreateAIRoleInstanceRequest): Promise<ApiResponse<AIRoleInstance>> => {
  return request({
    url: '/api/v1/system-config/ai-role-instances',
    method: 'post',
    data
  })
}

/**
 * 更新AI角色实例
 */
export const updateAIRoleInstance = (id: string, data: Partial<CreateAIRoleInstanceRequest>): Promise<ApiResponse<AIRoleInstance>> => {
  return request({
    url: `/api/v1/system-config/ai-role-instances/${id}`,
    method: 'put',
    data
  })
}

/**
 * 获取AI角色实例详情
 */
export const getAIRoleInstance = (id: string): Promise<ApiResponse<{ instance: AIRoleInstance }>> => {
  return request({
    url: `/api/v1/system-config/ai-role-instances/${id}`,
    method: 'get'
  })
}

/**
 * 删除AI角色实例
 */
export const deleteAIRoleInstance = (id: string): Promise<ApiResponse<void>> => {
  return request({
    url: `/api/v1/system-config/ai-role-instances/${id}`,
    method: 'delete'
  })
}

/**
 * 使用AI角色实例
 */
export const useAIRoleInstance = (id: string, user_input: string, context?: any): Promise<ApiResponse<{
  response: string
  instance_name: string
  template_name: string
  provider_name: string
}>> => {
  return request({
    url: `/api/v1/system-config/ai-role-instances/${id}/use`,
    method: 'post',
    data: { user_input, context }
  })
}

/**
 * 流式使用AI角色实例
 */
export const useAIRoleInstanceStream = async (
  id: string,
  user_input: string,
  context?: any,
  onChunk?: (chunk: { content?: string; error?: string; done?: boolean }) => void
): Promise<void> => {
  const response = await fetch(`/api/v1/system-config/ai-role-instances/${id}/use-stream`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ user_input, context })
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const reader = response.body?.getReader()
  if (!reader) {
    throw new Error('无法获取响应流')
  }

  const decoder = new TextDecoder()

  try {
    while (true) {
      const { done, value } = await reader.read()

      if (done) break

      const chunk = decoder.decode(value, { stream: true })
      const lines = chunk.split('\n')

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6))
            if (onChunk) {
              onChunk(data)
            }
            if (data.done) {
              return
            }
          } catch (e) {
            console.warn('解析SSE数据失败:', e)
          }
        }
      }
    }
  } finally {
    reader.releaseLock()
  }
}
