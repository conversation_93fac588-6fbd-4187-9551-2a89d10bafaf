<template>
  <div class="ai-roles-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>AI角色管理</h1>
        <p>创建和管理AI角色实例，让AI参与到您的运营和创作过程中</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog" :icon="Plus">
          创建AI角色
        </el-button>
      </div>
    </div>

    <!-- 角色列表 -->
    <div class="roles-list">
      <el-table 
        :data="roles" 
        :loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="name" label="角色名称" width="200">
          <template #default="{ row }">
            <div class="role-name">
              <el-avatar :size="32" :src="getRoleAvatar(row)" />
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" min-width="200" />
        
        <el-table-column prop="template_name" label="基于模板" width="150">
          <template #default="{ row }">
            <el-tag size="small" type="info">{{ row.template_name || '未知' }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="provider_name" label="AI提供商" width="150">
          <template #default="{ row }">
            <div>
              <el-tag size="small" :type="getProviderTypeColor(row.provider_type)">
                {{ row.provider_name || '未知' }}
              </el-tag>
              <div v-if="row.model" style="margin-top: 4px;">
                <el-tag size="small" type="info">{{ row.model }}</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="expertise" label="专业领域" width="200">
          <template #default="{ row }">
            <div class="expertise-tags">
              <el-tag 
                v-for="skill in row.expertise?.slice(0, 2)" 
                :key="skill" 
                size="small"
                style="margin-right: 4px;"
              >
                {{ skill }}
              </el-tag>
              <span v-if="row.expertise?.length > 2" class="more-count">
                +{{ row.expertise.length - 2 }}
              </span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="usage_count" label="使用次数" width="100" />
        
        <el-table-column prop="enabled" label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.enabled"
              @change="toggleRoleStatus(row)"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="chatWithRole(row)">
              对话
            </el-button>
            <el-button size="small" @click="editRole(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteRole(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'create' ? '创建AI角色' : '编辑AI角色'"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="角色名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入角色名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="基于模板" prop="template_id">
              <el-select v-model="form.template_id" placeholder="请选择模板" style="width: 100%">
                <el-option
                  v-for="template in templates"
                  :key="template.id"
                  :label="template.name"
                  :value="template.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="角色描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请描述这个AI角色的用途和特点"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="AI提供商" prop="provider_id">
              <el-select
                v-model="form.provider_id"
                placeholder="请选择AI提供商"
                style="width: 100%"
                @change="onProviderChange"
              >
                <el-option
                  v-for="provider in providers"
                  :key="provider.id"
                  :label="`${provider.name} (${provider.type})`"
                  :value="provider.id"
                >
                  <div style="display: flex; justify-content: space-between;">
                    <span>{{ provider.name }}</span>
                    <el-tag size="small" :type="getProviderTypeColor(provider.type)">
                      {{ provider.type }}
                    </el-tag>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="AI模型" prop="model">
              <el-select
                v-model="form.model"
                placeholder="请选择AI模型"
                style="width: 100%"
                :disabled="!selectedProvider"
              >
                <el-option
                  v-for="model in availableModels"
                  :key="model.value"
                  :label="model.label"
                  :value="model.value"
                >
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>{{ model.label }}</span>
                    <el-tag
                      v-if="selectedProvider?.model === model.value"
                      size="small"
                      type="success"
                    >
                      提供商配置
                    </el-tag>
                  </div>
                </el-option>
              </el-select>
              <div v-if="selectedProvider?.model" class="form-tip">
                <small>✓ 使用提供商配置的模型: {{ selectedProvider.model }}</small>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="语调风格" prop="tone">
              <el-select v-model="form.tone" placeholder="请选择语调风格" style="width: 100%">
                <el-option label="专业" value="professional" />
                <el-option label="友好" value="friendly" />
                <el-option label="幽默" value="humorous" />
                <el-option label="严肃" value="serious" />
                <el-option label="创意" value="creative" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大令牌数">
              <el-input-number
                v-model="form.max_tokens"
                :min="100"
                :max="8000"
                :step="100"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="个性描述">
          <el-input
            v-model="form.personality"
            type="textarea"
            :rows="2"
            placeholder="描述这个AI角色的个性特点，如：热情、耐心、专业等"
          />
        </el-form-item>
        
        <el-form-item label="专业领域">
          <el-select
            v-model="form.expertise"
            multiple
            filterable
            allow-create
            placeholder="选择或输入专业领域"
            style="width: 100%"
          >
            <el-option label="内容创作" value="content-creation" />
            <el-option label="视频制作" value="video-production" />
            <el-option label="文案写作" value="copywriting" />
            <el-option label="营销策划" value="marketing" />
            <el-option label="数据分析" value="data-analysis" />
            <el-option label="用户体验" value="user-experience" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="工作流类型">
          <el-checkbox-group v-model="form.workflow_types">
            <el-checkbox label="content-generation">内容生成</el-checkbox>
            <el-checkbox label="script-writing">脚本编写</el-checkbox>
            <el-checkbox label="idea-brainstorm">创意头脑风暴</el-checkbox>
            <el-checkbox label="content-optimization">内容优化</el-checkbox>
            <el-checkbox label="trend-analysis">趋势分析</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="启用记忆">
              <el-switch v-model="form.memory_enabled" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="自动建议">
              <el-switch v-model="form.auto_suggestions" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="启用角色">
              <el-switch v-model="form.enabled" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="上下文窗口">
              <el-slider
                v-model="form.context_window"
                :min="1000"
                :max="8000"
                :step="500"
                show-input
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创造性 (Temperature)">
              <el-slider
                v-model="form.temperature"
                :min="0"
                :max="2"
                :step="0.1"
                show-input
                style="width: 100%"
              />
              <div class="form-tip">
                <small>0 = 更保守准确，2 = 更有创意随机</small>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ dialogMode === 'create' ? '创建' : '保存' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 对话框 -->
    <el-dialog
      v-model="chatVisible"
      :title="`与 ${currentChatRole?.name} 对话`"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="chat-container">
        <div class="chat-messages" ref="messagesContainer">
          <div
            v-for="(message, index) in chatMessages"
            :key="index"
            :class="['message', message.role === 'user' ? 'user-message' : 'ai-message']"
          >
            <div class="message-avatar">
              <el-avatar v-if="message.role === 'user'" :size="32">用户</el-avatar>
              <el-avatar v-else :size="32" :src="getRoleAvatar(currentChatRole!)"></el-avatar>
            </div>
            <div class="message-content">
              <div class="message-text">
                {{ message.content }}
                <span v-if="message.role === 'assistant' && chatLoading && index === chatMessages.length - 1" class="typing-indicator">▊</span>
              </div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>
          <div v-if="chatLoading" class="message ai-message">
            <div class="message-avatar">
              <el-avatar :size="32" :src="getRoleAvatar(currentChatRole!)"></el-avatar>
            </div>
            <div class="message-content">
              <div class="message-text">
                <el-icon class="is-loading"><Loading /></el-icon>
                正在思考中...
              </div>
            </div>
          </div>
        </div>
        <div class="chat-input">
          <el-input
            v-model="chatInput"
            type="textarea"
            :rows="3"
            placeholder="输入您的消息..."
            @keydown.ctrl.enter="sendMessage"
          />
          <div class="chat-actions">
            <el-button @click="clearChat">清空对话</el-button>
            <el-button type="primary" @click="sendMessage" :loading="chatLoading" :disabled="!chatInput.trim()">
              发送 (Ctrl+Enter)
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Loading } from '@element-plus/icons-vue'
import {
  getAIRoleInstances,
  createAIRoleInstance,
  updateAIRoleInstance,
  deleteAIRoleInstance,
  getAIProviders,
  getAIRoleTemplates,
  useAIRoleInstance,
  useAIRoleInstanceStream
} from '@/api/system-config'

// 类型定义
interface AIRole {
  id: string
  name: string
  description: string
  template_id: string
  template_name?: string
  template_category?: string
  provider_id: string
  provider_name?: string
  provider_type?: string
  model: string
  max_tokens: number
  temperature: number
  personality: string
  expertise: string[]
  tone: string
  workflow_types: string[]
  auto_suggestions: boolean
  memory_enabled: boolean
  context_window: number
  enabled: boolean
  usage_count: number
  created_at: string
  updated_at: string
  last_used_at?: string
}

interface AITemplate {
  id: string
  name: string
  category: string
}

interface AIProvider {
  id: string
  name: string
  type: string
}

// 响应式数据
const loading = ref(false)
const roles = ref<AIRole[]>([])
const templates = ref<AITemplate[]>([])
const providers = ref<AIProvider[]>([])
const dialogVisible = ref(false)
const dialogMode = ref<'create' | 'edit'>('create')
const submitting = ref(false)
const formRef = ref()

const form = reactive({
  name: '',
  description: '',
  template_id: '',
  provider_id: '',
  model: '',
  personality: '',
  expertise: [] as string[],
  tone: 'professional',
  workflow_types: [] as string[],
  auto_suggestions: true,
  memory_enabled: true,
  context_window: 4000,
  max_tokens: 2000,
  temperature: 0.7,
  enabled: true
})

const currentEditId = ref('')

// 模型选择相关
const selectedProvider = ref<AIProvider | null>(null)
const availableModels = ref<Array<{label: string, value: string}>>([])

// 预定义的模型列表
const modelOptions = {
  openai: [
    { label: 'GPT-4', value: 'gpt-4' },
    { label: 'GPT-4 Turbo', value: 'gpt-4-turbo-preview' },
    { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' },
    { label: 'GPT-3.5 Turbo 16K', value: 'gpt-3.5-turbo-16k' }
  ],
  azure: [
    { label: 'GPT-4', value: 'gpt-4' },
    { label: 'GPT-4 Turbo', value: 'gpt-4-turbo' },
    { label: 'GPT-3.5 Turbo', value: 'gpt-35-turbo' },
    { label: 'GPT-3.5 Turbo 16K', value: 'gpt-35-turbo-16k' }
  ],
  claude: [
    { label: 'Claude 3 Opus', value: 'claude-3-opus-20240229' },
    { label: 'Claude 3 Sonnet', value: 'claude-3-sonnet-20240229' },
    { label: 'Claude 3 Haiku', value: 'claude-3-haiku-20240307' },
    { label: 'Claude 2.1', value: 'claude-2.1' },
    { label: 'Claude 2', value: 'claude-2' }
  ],
  custom: [
    { label: '自定义模型', value: 'custom-model' }
  ]
}

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入角色描述', trigger: 'blur' }
  ],
  template_id: [
    { required: true, message: '请选择模板', trigger: 'change' }
  ],
  provider_id: [
    { required: true, message: '请选择AI提供商', trigger: 'change' }
  ],
  model: [
    { required: true, message: '请选择AI模型', trigger: 'change' }
  ]
}

// 获取角色列表
const fetchRoles = async () => {
  try {
    loading.value = true
    const response = await getAIRoleInstances()
    if (response.data) {
      roles.value = response.data.items || []
    }
  } catch (error: any) {
    console.error('获取角色列表失败:', error)
    ElMessage.error('获取角色列表失败')
  } finally {
    loading.value = false
  }
}

// 获取模板列表
const fetchTemplates = async () => {
  try {
    const response = await getAIRoleTemplates()
    if (response.data) {
      templates.value = response.data.items || []
    }
  } catch (error: any) {
    console.error('获取模板列表失败:', error)
  }
}

// 获取提供商列表
const fetchProviders = async () => {
  try {
    const response = await getAIProviders()
    if (response.data) {
      providers.value = response.data.items || []
    }
  } catch (error: any) {
    console.error('获取提供商列表失败:', error)
  }
}

// 显示创建对话框
const showCreateDialog = () => {
  dialogMode.value = 'create'
  resetForm()
  dialogVisible.value = true
}

// 提供商变化处理
const onProviderChange = (providerId: string) => {
  const provider = providers.value.find(p => p.id === providerId)
  selectedProvider.value = provider || null

  if (provider) {
    // 从提供商配置中获取模型信息
    if (provider.model) {
      // 如果提供商配置了具体模型，直接使用
      availableModels.value = [
        { label: provider.model, value: provider.model }
      ]
      form.model = provider.model
    } else {
      // 如果没有配置具体模型，使用预定义的模型列表
      availableModels.value = modelOptions[provider.type] || []
      form.model = ''
    }
  } else {
    availableModels.value = []
    form.model = ''
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    name: '',
    description: '',
    template_id: '',
    provider_id: '',
    model: '',
    personality: '',
    expertise: [],
    tone: 'professional',
    workflow_types: [],
    auto_suggestions: true,
    memory_enabled: true,
    context_window: 4000,
    max_tokens: 2000,
    temperature: 0.7,
    enabled: true
  })
  currentEditId.value = ''
  selectedProvider.value = null
  availableModels.value = []
}

// 提交表单
const submitForm = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    if (dialogMode.value === 'create') {
      await createAIRoleInstance({
        name: form.name,
        description: form.description,
        template_id: form.template_id,
        provider_id: form.provider_id,
        model: form.model,
        personality: form.personality,
        expertise: form.expertise,
        tone: form.tone,
        workflow_types: form.workflow_types,
        auto_suggestions: form.auto_suggestions,
        memory_enabled: form.memory_enabled,
        context_window: form.context_window,
        max_tokens: form.max_tokens,
        temperature: form.temperature,
        enabled: form.enabled
      })
      ElMessage.success('角色创建成功')
    } else {
      await updateAIRoleInstance(currentEditId.value, {
        name: form.name,
        description: form.description,
        template_id: form.template_id,
        provider_id: form.provider_id,
        model: form.model,
        personality: form.personality,
        expertise: form.expertise,
        tone: form.tone,
        workflow_types: form.workflow_types,
        auto_suggestions: form.auto_suggestions,
        memory_enabled: form.memory_enabled,
        context_window: form.context_window,
        max_tokens: form.max_tokens,
        temperature: form.temperature,
        enabled: form.enabled
      })
      ElMessage.success('角色更新成功')
    }

    dialogVisible.value = false
    await fetchRoles()
  } catch (error: any) {
    console.error('提交失败:', error)
    ElMessage.error(`提交失败: ${error.message || error}`)
  } finally {
    submitting.value = false
  }
}

// 工具函数
const getRoleAvatar = (role: AIRole) => {
  // 根据角色名称生成头像
  return `https://api.dicebear.com/7.x/avataaars/svg?seed=${role.name}`
}

const getProviderTypeColor = (type: string) => {
  const colorMap = {
    openai: 'success',
    azure: 'primary',
    claude: 'warning',
    custom: 'info'
  }
  return colorMap[type] || 'info'
}

// 角色操作
const toggleRoleStatus = async (role: AIRole) => {
  const originalStatus = role.enabled
  try {
    await updateAIRoleInstance(role.id, { enabled: role.enabled })
    ElMessage.success(`角色已${role.enabled ? '启用' : '禁用'}`)
  } catch (error: any) {
    // 如果失败，恢复原状态
    role.enabled = originalStatus
    console.error('更新角色状态失败:', error)
    ElMessage.error('更新角色状态失败')
  }
}

const editRole = (role: AIRole) => {
  dialogMode.value = 'edit'
  currentEditId.value = role.id

  // 填充表单数据
  Object.assign(form, {
    name: role.name,
    description: role.description,
    template_id: role.template_id,
    provider_id: role.provider_id,
    model: role.model,
    personality: role.personality,
    expertise: role.expertise || [],
    tone: role.tone,
    workflow_types: role.workflow_types || [],
    auto_suggestions: role.auto_suggestions,
    memory_enabled: role.memory_enabled,
    context_window: role.context_window,
    max_tokens: role.max_tokens,
    temperature: role.temperature,
    enabled: role.enabled
  })

  // 设置选中的提供商
  const provider = providers.value.find(p => p.id === role.provider_id)
  if (provider) {
    selectedProvider.value = provider
    onProviderChange(provider.id)
  }

  dialogVisible.value = true
}

const deleteRole = async (role: AIRole) => {
  try {
    await ElMessageBox.confirm(`确定要删除角色 "${role.name}" 吗？`, '确认删除', {
      type: 'warning'
    })

    await deleteAIRoleInstance(role.id)
    ElMessage.success('角色删除成功')
    await fetchRoles()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除角色失败:', error)
      ElMessage.error(`删除角色失败: ${error.message || error}`)
    }
  }
}

// 对话相关
const chatVisible = ref(false)
const currentChatRole = ref<AIRole | null>(null)
const chatMessages = ref<Array<{
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}>>([])
const chatInput = ref('')
const chatLoading = ref(false)
const messagesContainer = ref()

const chatWithRole = (role: AIRole) => {
  currentChatRole.value = role
  chatMessages.value = []
  chatInput.value = ''
  chatVisible.value = true
}

const sendMessage = async () => {
  if (!chatInput.value.trim() || chatLoading.value) return

  const userMessage = chatInput.value.trim()
  chatInput.value = ''

  // 添加用户消息
  chatMessages.value.push({
    role: 'user',
    content: userMessage,
    timestamp: new Date()
  })

  // 滚动到底部
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })

  chatLoading.value = true

  // 添加一个空的AI消息，用于流式更新
  const aiMessageIndex = chatMessages.value.length
  chatMessages.value.push({
    role: 'assistant',
    content: '',
    timestamp: new Date()
  })

  try {
    // 使用流式API
    await useAIRoleInstanceStream(
      currentChatRole.value!.id,
      userMessage,
      undefined,
      (chunk) => {
        if (chunk.error) {
          // 处理错误
          chatMessages.value[aiMessageIndex].content = `抱歉，发生了错误: ${chunk.error}`
          chatLoading.value = false
        } else if (chunk.content) {
          // 追加内容
          chatMessages.value[aiMessageIndex].content += chunk.content

          // 滚动到底部
          nextTick(() => {
            if (messagesContainer.value) {
              messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
            }
          })
        } else if (chunk.done) {
          // 流式响应完成
          chatLoading.value = false
        }
      }
    )
  } catch (error: any) {
    console.error('发送消息失败:', error)
    ElMessage.error(`发送消息失败: ${error.message || error}`)

    // 更新错误消息
    chatMessages.value[aiMessageIndex].content = '抱歉，我现在无法回复您的消息。请检查AI提供商配置或稍后再试。'
    chatLoading.value = false
  }
}

const clearChat = () => {
  chatMessages.value = []
}

const formatTime = (timestamp: Date) => {
  return timestamp.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 初始化
onMounted(() => {
  fetchRoles()
  fetchTemplates()
  fetchProviders()
})
</script>

<style scoped>
.ai-roles-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
}

.roles-list {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.role-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.expertise-tags {
  display: flex;
  align-items: center;
  gap: 4px;
}

.more-count {
  font-size: 12px;
  color: #909399;
}

.form-tip {
  margin-top: 4px;
  color: #909399;
  font-size: 12px;
}

.el-select .el-select__tags {
  max-width: calc(100% - 30px);
}

/* 对话样式 */
.chat-container {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.message {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.user-message {
  flex-direction: row-reverse;
}

.user-message .message-content {
  text-align: right;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.message-text {
  background: white;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
  white-space: pre-wrap;
}

.user-message .message-text {
  background: #409eff;
  color: white;
}

.ai-message .message-text {
  background: white;
  color: #303133;
}

.message-time {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.chat-input {
  border-top: 1px solid #ebeef5;
  padding-top: 16px;
}

.chat-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}

/* 流式响应动画 */
.typing-indicator {
  animation: blink 1s infinite;
  color: #409eff;
  font-weight: bold;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
</style>
